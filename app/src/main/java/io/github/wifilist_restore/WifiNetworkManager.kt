@file:Suppress("DEPRECATION")

package io.github.wifilist_restore

import android.annotation.SuppressLint
import android.content.AttributionSource
import android.content.Context
import android.net.wifi.WifiConfiguration
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.os.ParcelFileDescriptor
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import moe.shizuku.server.IShizukuService
import org.lsposed.hiddenapibypass.HiddenApiBypass
import rikka.shizuku.Shizuku
import rikka.shizuku.ShizukuBinderWrapper
import rikka.shizuku.SystemServiceHelper

class WifiNetworkManager() {
    companion object {
        private const val TAG = "WifiNetworkManager"
    }

    init {
        HiddenApiBypass.addHiddenApiExemptions("")
        loadWifiNetworks()
    }

    val isRoot get() = Shizuku.getUid() == 0

    @SuppressLint("PrivateApi")
    fun loadWifiNetworks() {
        val base = Class.forName("android.net.wifi.IWifiManager")
        val stub = Class.forName("android.net.wifi.IWifiManager\$Stub")
        val asInterface = stub.getMethod("asInterface", IBinder::class.java)
        val iwm = asInterface.invoke(
            null, ShizukuBinderWrapper(SystemServiceHelper.getSystemService(Context.WIFI_SERVICE))
        )

        base.methods.forEach { println(it.name) }

        val user = when (Shizuku.getUid()) {
            0 -> "root"
            1000 -> "system"
            2000 -> "shell"
            else -> throw IllegalArgumentException("Unknown Shizuku user ${Shizuku.getUid()}")
        }
        val pkg = "com.android.shell"

        val privilegedConfigs = if (Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2) {
            val getPrivilegedConfiguredNetworks = base.getMethod(
                "getPrivilegedConfiguredNetworks",
                String::class.java,
                String::class.java,
                Bundle::class.java
            )
            getPrivilegedConfiguredNetworks.invoke(
                iwm, user, pkg, Bundle().apply {
                    putParcelable(
                        "EXTRA_PARAM_KEY_ATTRIBUTION_SOURCE",
                        AttributionSource::class.java.getConstructor(
                            Int::class.java,
                            String::class.java,
                            String::class.java,
                            Set::class.java,
                            AttributionSource::class.java
                        ).newInstance(Shizuku.getUid(), pkg, pkg, null as Set<String>?, null)
                    )
                })
        } else {
            try {
                val getPrivilegedConfiguredNetworks = base.getMethod(
                    "getPrivilegedConfiguredNetworks", String::class.java, String::class.java
                )
                getPrivilegedConfiguredNetworks.invoke(iwm, user, pkg)
            } catch (_: NoSuchMethodException) {
                val getPrivilegedConfiguredNetworks = base.getMethod(
                    "getPrivilegedConfiguredNetworks",
                    String::class.java,
                    String::class.java,
                    Bundle::class.java,
                )
                getPrivilegedConfiguredNetworks.invoke(iwm, user, pkg, null)
            }
        }

        @Suppress("UNCHECKED_CAST") val privilegedConfigsList = privilegedConfigs?.let {
            it::class.java.getMethod("getList").invoke(it) as List<WifiConfiguration>
        } ?: listOf()

        val items = privilegedConfigsList.sortedBy { it.SSID.lowercase() }
        items.forEach {
            it.networkId = -1
            it.SSID = ""
            it.hiddenSSID = true
            it.preSharedKey = ""
            it.setSecurityParams(WifiConfiguration.SECURITY_TYPE_OWE)
            Log.d(TAG, it.toString())
        }
    }

    suspend fun addMultipleWifiNetworks(networks: List<WiFiExportItem>) =
        withContext(Dispatchers.IO) {
            runCatching {
                for (network in networks) {
                    val command = buildString {
                        append("cmd wifi add-network \"${network.ssid}\"")

                        when (network.security) {
                            "OPEN" -> append(" open")
                            "ENHANCED_OPEN" -> append(" owe")
                            "WPA3_PERSONAL" -> append(" wpa3 \"${network.password}\"")
                            "WPA_WPA2_PERSONAL" -> append(" wpa2 \"${network.password}\"")
                            else -> continue
                        }
                    }
                    val result = execute(command)
                    println(result)
                }
            }.fold(
                onSuccess = { Log.d(TAG, "Successfully added networks") },
                onFailure = { Log.e(TAG, "Failed to add networks: ${it.message}") },
            )
        }

    suspend fun removeMultipleWifiNetwork(networks: List<WiFiExportItem>) =
        withContext(Dispatchers.IO) {
            runCatching {
                val output = execute("cmd wifi list-networks").output.lineSequence().drop(1)
                    .mapNotNull { it.trim().takeIf(String::isNotBlank) }.map {
                        val firstSpaceIndex = it.indexOf(' ')
                        val lastSpaceIndex = it.lastIndexOf(' ')

                        val networkId = it.substring(0, firstSpaceIndex).trim()
                        val ssid = it.substring(firstSpaceIndex, lastSpaceIndex).trim()

                        networkId to ssid
                    }

                val networkIds = networks.mapNotNull { network ->
                    output.find { it.second == network.ssid.trim() }?.first
                }.toSet()

                networkIds.forEach { id ->
                    val result = execute("cmd wifi forget-network $id")
                    println(result)
                }
            }.fold(
                onSuccess = { Log.d(TAG, "Successfully removed networks") },
                onFailure = { Log.e(TAG, "Failed to remove networks: ${it.message}") },
            )
        }

    private fun execute(command: String): ShellResult = runCatching {
        val process = IShizukuService.Stub.asInterface(Shizuku.getBinder())
            .newProcess(arrayOf(if (isRoot) "su" else "sh", "-c", command), null, null)
        val output = process.inputStream.text.ifBlank { process.errorStream.text }
        val resultCode = process.waitFor()
        ShellResult(resultCode, output.trim())
    }.getOrElse { ShellResult(resultCode = -1, output = it.stackTraceToString()) }

    private val ParcelFileDescriptor.text
        get() = ParcelFileDescriptor.AutoCloseInputStream(this)
            .use { it.bufferedReader().readText() }
}