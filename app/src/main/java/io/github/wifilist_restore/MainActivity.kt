package io.github.wifilist_restore

import android.app.AlertDialog
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.net.toUri
import io.github.vinceglb.filekit.FileKit
import io.github.vinceglb.filekit.dialogs.init
import io.github.wifilist_restore.ui.theme.WifiListRestoreTheme
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import rikka.shizuku.Shizuku
import rikka.shizuku.ShizukuProvider

class MainActivity : ComponentActivity(), Shizuku.OnRequestPermissionResultListener,
    CoroutineScope by MainScope() {
    companion object {
        private const val SHIZUKU_PERMISSION = 10001
    }

    private val permResultListener =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (!isGranted) {
                showShizukuFailureDialog()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        FileKit.init(this)

        enableEdgeToEdge()
        setContent { WifiListRestoreTheme { SelectFile() } }

        if (!Shizuku.pingBinder()) {
            showShizukuFailureDialog()
            return
        }

        if (!hasShizukuPermission) {
            requestShizukuPermission()
        }
    }

    override fun onRequestPermissionResult(requestCode: Int, grantResult: Int) {
        if (grantResult != PackageManager.PERMISSION_GRANTED) {
            showShizukuFailureDialog()
        }
    }

    override fun onDestroy() {
        cancel()
        super.onDestroy()
    }

    private fun showShizukuFailureDialog() {
        AlertDialog.Builder(this).setTitle("Shizuku Required").setMessage(
            "Shizuku is required to use this app. If you have Shizuku installed, please start it. Otherwise, you can install Shizuku from the Play Store."
        ).setNegativeButton("") { _, _ -> finish() }.setCancelable(false).apply {
            try {
                packageManager.getApplicationInfo(ShizukuProvider.MANAGER_APPLICATION_ID, 0)
                setPositiveButton("Open Shizuku") { _, _ ->
                    val shizukuIntent = Intent(Intent.ACTION_MAIN)
                    shizukuIntent.component = ComponentName(
                        ShizukuProvider.MANAGER_APPLICATION_ID, "moe.shizuku.manager.MainActivity"
                    )
                    shizukuIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(shizukuIntent)
                    finish()
                }
            } catch (_: PackageManager.NameNotFoundException) {
                setPositiveButton("Install Shizuku") { _, _ ->
                    launchUrl(
                        "https://play.google.com/store/apps/details?id=${ShizukuProvider.MANAGER_APPLICATION_ID}"
                    )
                    finish()
                }
            }
        }.show()
    }

    private fun requestShizukuPermission() {
        if (Shizuku.isPreV11() || Shizuku.getVersion() < 11) {
            permResultListener.launch(ShizukuProvider.PERMISSION)
        } else {
            Shizuku.addRequestPermissionResultListener(this)
            Shizuku.requestPermission(SHIZUKU_PERMISSION)
        }
    }
}

val Context.hasShizukuPermission: Boolean
    get() {
        if (!Shizuku.pingBinder()) {
            return false
        }

        return if (Shizuku.isPreV11() || Shizuku.getVersion() < 11) {
            checkCallingOrSelfPermission(ShizukuProvider.PERMISSION) == PackageManager.PERMISSION_GRANTED
        } else {
            Shizuku.checkSelfPermission() == PackageManager.PERMISSION_GRANTED
        }
    }

fun Context.launchUrl(url: String) {
    try {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = url.toUri()

        startActivity(intent)
    } catch (_: Exception) {
    }
}
