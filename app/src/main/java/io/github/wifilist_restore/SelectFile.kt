package io.github.wifilist_restore

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import io.github.vinceglb.filekit.dialogs.FileKitMode
import io.github.vinceglb.filekit.dialogs.FileKitType
import io.github.vinceglb.filekit.dialogs.compose.rememberFilePickerLauncher
import io.github.vinceglb.filekit.extension
import io.github.vinceglb.filekit.readString
import io.github.wifilist_restore.ui.theme.WifiListRestoreTheme
import kotlinx.coroutines.launch


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectFile() {
    val wifiManager = remember { WifiNetworkManager() }
    val networks = remember { mutableStateListOf<WiFiExportItem>() }
    var isLoading by remember { mutableStateOf(false) }

    val scope = rememberCoroutineScope()
    val launcher = rememberFilePickerLauncher(
        mode = FileKitMode.Single,
        type = FileKitType.File(extensions = listOf("json", "csv")),
        title = "Select exported WiFiList file"
    ) { file ->
        scope.launch {
            val rawString = file?.readString() ?: return@launch
            val items = when (file.extension) {
                "json" -> WiFiExportItem.fromJson(rawString)
                "csv" -> WiFiExportItem.fromCsv(rawString)
                else -> return@launch
            }
            isLoading = true

            networks.clear()
            networks.addAll(items)
            wifiManager.addMultipleWifiNetworks(items)

            isLoading = false
        }
    }

    Scaffold(topBar = {
        TopAppBar(title = { Text("Select file") }, actions = {
            if (networks.isNotEmpty()) {
                IconButton(onClick = {
                    scope.launch {
                        isLoading = true

                        wifiManager.removeMultipleWifiNetwork(networks)
                        networks.clear()

                        isLoading = false
                    }
                }) {
                    Icon(imageVector = Icons.Filled.Clear, contentDescription = null)
                }
            }
        })
    }, floatingActionButton = {
        ExtendedFloatingActionButton(
            text = { Text("Select file") },
            onClick = { launcher.launch() },
            icon = { Icon(imageVector = Icons.Filled.Add, contentDescription = null) },
        )
    }) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            contentAlignment = Alignment.Center
        ) {
            if (isLoading) {
                CircularProgressIndicator()
            } else {
                LazyColumn(
                    contentPadding = PaddingValues(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(items = networks) { network ->
                        ElevatedCard(modifier = Modifier.fillMaxWidth()) {
                            Column(
                                modifier = Modifier.padding(8.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(text = network.ssid)
                                Text(text = network.security)
                                if (network.password.isNotBlank()) {
                                    Text(text = network.password)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun SelectFilePreview() {
    WifiListRestoreTheme { SelectFile() }
}