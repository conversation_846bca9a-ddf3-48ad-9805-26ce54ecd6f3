package io.github.wifilist_restore

import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken


data class WiFiExportItem(
    val ssid: String,
    val security: String,
    val password: String,
) {
    companion object
}

fun WiFiExportItem.Companion.fromJson(json: String): List<WiFiExportItem> {
    val gson = GsonBuilder().create()
    val type = object : TypeToken<List<WiFiExportItem>>() {}.type
    return gson.fromJson(json, type)
}

fun WiFiExportItem.Companion.fromCsv(csv: String): List<WiFiExportItem> {
    val lines = csv.lines().drop(1)
    return lines.mapNotNull { line ->
        val columns = line.split(",")
        if (columns.size != 3) return@mapNotNull null
        WiFiExportItem(ssid = columns[0], password = columns[1], security = columns[2])
    }
}